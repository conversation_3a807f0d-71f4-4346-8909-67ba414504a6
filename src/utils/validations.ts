import * as Yup from 'yup';

// Phone number normalization function
export const normalizePhoneNumber = (phone: string): string => {
  if (!phone) return '';

  // Remove all non-digit characters except +
  let cleaned = phone.replace(/[^\d+]/g, '');

  // Remove + if present
  cleaned = cleaned.replace(/^\+/, '');

  // If phone starts with 8, replace with 7
  if (cleaned.startsWith('8')) {
    cleaned = '7' + cleaned.substring(1);
  }

  // Handle different cases based on length and starting digits
  if (cleaned.startsWith('7') && cleaned.length === 11) {
    // Already has Kazakhstan country code and correct length (7XXXXXXXXXX)
    // Do nothing, it's already correct
  } else if (cleaned.startsWith('7') && cleaned.length === 10) {
    // This is a local Kazakhstan number starting with 7 (7XXXXXXXXX)
    // Add country code 7 to make it 77XXXXXXXXX
    cleaned = '7' + cleaned;
  } else if (cleaned.length === 10) {
    // This is a local Kazakhstan number without country code (XXXXXXXXXX)
    // Add country code 7
    cleaned = '7' + cleaned;
  } else if (cleaned.startsWith('7') && cleaned.length > 11) {
    // Too many digits, truncate to 11
    cleaned = cleaned.substring(0, 11);
  } else if (cleaned.length > 10) {
    // Too many digits for local number, add 7 and truncate
    cleaned = '7' + cleaned.substring(0, 10);
  } else {
    // Default case: add Kazakhstan country code
    cleaned = '7' + cleaned;
  }

  // Add + prefix
  return '+' + cleaned;
};

// Phone number formatting for display (Kazakhstan format: +7 XXX XXX XX XX)
export const formatPhoneForDisplay = (phone: string): string => {
  if (!phone) return '';
  
  // Normalize first
  const normalized = normalizePhoneNumber(phone);
  
  // Remove + and extract digits
  const digits = normalized.replace(/^\+/, '');
  
  // Format as +7 XXX XXX XX XX
  if (digits.length === 11 && digits.startsWith('7')) {
    return `+7 ${digits.slice(1, 4)} ${digits.slice(4, 7)} ${digits.slice(7, 9)} ${digits.slice(9, 11)}`;
  }
  
  return normalized; // Fallback to normalized version
};

// Phone input mask function - formats as user types
export const formatPhoneInput = (value: string): string => {
  if (!value) return '';
  
  // Remove all non-digits
  const digits = value.replace(/\D/g, '');
  
  // Handle different input scenarios
  let formattedDigits = digits;
  
  // If starts with 8, replace with 7
  if (formattedDigits.startsWith('8')) {
    formattedDigits = '7' + formattedDigits.substring(1);
  }
  
  // If doesn't start with 7, add 7
  if (formattedDigits && !formattedDigits.startsWith('7')) {
    formattedDigits = '7' + formattedDigits;
  }
  
  // Limit to 11 digits max (7 + 10 digits)
  if (formattedDigits.length > 11) {
    formattedDigits = formattedDigits.substring(0, 11);
  }
  
  // Format progressively as user types
  if (formattedDigits.length === 0) {
    return '';
  } else if (formattedDigits.length === 1) {
    return '+7';
  } else if (formattedDigits.length <= 4) {
    return `+7 ${formattedDigits.slice(1)}`;
  } else if (formattedDigits.length <= 7) {
    return `+7 ${formattedDigits.slice(1, 4)} ${formattedDigits.slice(4)}`;
  } else if (formattedDigits.length <= 9) {
    return `+7 ${formattedDigits.slice(1, 4)} ${formattedDigits.slice(4, 7)} ${formattedDigits.slice(7)}`;
  } else {
    return `+7 ${formattedDigits.slice(1, 4)} ${formattedDigits.slice(4, 7)} ${formattedDigits.slice(7, 9)} ${formattedDigits.slice(9)}`;
  }
};

// Get raw digits from formatted phone for validation/storage
export const getPhoneDigits = (formattedPhone: string): string => {
  return formattedPhone.replace(/\D/g, '');
};

// Validate Kazakhstan phone number format
export const isValidKazakhstanPhone = (phone: string): boolean => {
  const digits = getPhoneDigits(phone);
  // Should be 11 digits starting with 7
  return digits.length === 11 && digits.startsWith('7');
};

// Basic field validations
export const validatePhone = (phone: string) =>
  /^\+7\s?[\(\s]?\d{3}[\)\s-]?\s?\d{3}[\s-]?\d{2}[\s-]?\d{2}$/.test(phone);

export const validateEmail = (email: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

export const validateIIN = (iin: string) =>
  /^\d{12}$/.test(iin);

export const validateIDNumber = (idNumber: string) =>
  /^\d{9}$/.test(idNumber);

// Yup validation schemas for each step
export const step1Schema = Yup.object({
  visaDestination: Yup.string()
    .required('Выберите страну для получения визы')
    .nullable(),
  otherVisaDestination: Yup.string().when('visaDestination', {
    is: 'other',
    then: (schema) => schema.required('Укажите страну'),
    otherwise: (schema) => schema.nullable(),
  }),
});

export const step2Schema = Yup.object().shape({
  surname: Yup.string()
    .matches(/^[A-Za-z\s\-]+$/, 'Фамилия должна содержать только латинские буквы')
    .required('Фамилия обязательна'),
  name: Yup.string()
    .matches(/^[A-Za-z\s\-]+$/, 'Имя должно содержать только латинские буквы')
    .required('Имя обязательно'),
  dateOfBirth: Yup.date()
    .required('Дата рождения обязательна')
    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Возраст должен быть не менее 18 лет')
    .min(new Date(Date.now() - 120 * 365 * 24 * 60 * 60 * 1000), 'Дата рождения не может быть более 120 лет назад'),
  citizenship: Yup.string().required('Гражданство обязательно'),
  passportNumber: Yup.string().nullable(),
  passportIssueDate: Yup.date()
    .nullable()
    .when('passportNumber', {
      is: (passportNumber: string) => passportNumber && passportNumber.trim() !== '',
      then: (schema) => schema
        .required('Дата выдачи паспорта обязательна')
        .max(new Date(), 'Дата выдачи не может быть в будущем'),
      otherwise: (schema) => schema.nullable()
    }),
  passportExpiryDate: Yup.date()
    .nullable()
    .when('passportNumber', {
      is: (passportNumber: string) => passportNumber && passportNumber.trim() !== '',
      then: (schema) => schema
        .required('Дата окончания паспорта обязательна')
        .min(new Date(), 'Паспорт должен быть действительным'),
      otherwise: (schema) => schema.nullable()
    }),
  iin: Yup.string()
    .nullable()
    .when('citizenship', {
      is: (citizenship: string) => citizenship === 'Казахстан',
      then: (schema) => schema
        .required('ИИН обязателен для граждан Казахстана')
        .matches(/^\d{12}$/, 'ИИН должен содержать ровно 12 цифр'),
      otherwise: (schema) => schema
        .test('iin-format', 'ИИН должен содержать 12 цифр', function(value) {
          // IIN is optional for non-Kazakhstan citizens but if provided must be valid
          return !value || value.trim() === '' || /^\d{12}$/.test(value);
        })
    }),
  idNumber: Yup.string()
    .nullable()
    .test('id-format', 'Номер удостоверения должен содержать 9 цифр', function(value) {
      // ID number is optional but if provided must be valid
      return !value || value.trim() === '' || /^\d{9}$/.test(value);
    }),
}).test('passport-or-id-or-iin', 'Необходимо указать номер паспорта, ИИН или номер удостоверения личности', function(values) {
  const { passportNumber, idNumber, iin } = values || {};
  // At least one of the three identification methods must be provided
  return Boolean(
    (passportNumber && passportNumber.trim() !== '') || 
    (idNumber && idNumber.trim() !== '') ||
    (iin && iin.trim() !== '')
  );
});

export const step3Schema = Yup.object({
  fullNameCyrillic: Yup.string()
    .matches(/^[\u0400-\u04FFёЁәғқңөұүһіӘҒҚҢӨҰҮҺІ\s\-]+$/, 'ФИО должно содержать только кириллические буквы (включая казахские)')
    .required('ФИО на кириллице обязательно'),
  hasOtherNames: Yup.boolean().required(),
  otherNames: Yup.string().when('hasOtherNames', {
    is: true,
    then: (schema) => schema.required('Необходимо указать другие имена'),
    otherwise: (schema) => schema.optional(),
  }),
  gender: Yup.string().oneOf(['male', 'female']).required('Пол обязателен'),
  maritalStatus: Yup.string().notOneOf([''], 'Семейное положение обязательно').required('Семейное положение обязательно'),
  cityOfBirth: Yup.string().required('Город рождения обязателен'),
  countryOfBirth: Yup.string().required('Страна рождения обязательна'),
  hasOtherCitizenship: Yup.boolean().required(),
  otherCitizenship: Yup.string().when('hasOtherCitizenship', {
    is: true,
    then: (schema) => schema.required('Необходимо указать другое гражданство'),
    otherwise: (schema) => schema.optional(),
  }),
  isPermanentResidentOtherCountry: Yup.boolean().required(),
  permanentResidenceCountry: Yup.string().when('isPermanentResidentOtherCountry', {
    is: true,
    then: (schema) => schema.required('Необходимо указать страну постоянного проживания'),
    otherwise: (schema) => schema.optional(),
  }),
  nationality: Yup.string().required('Национальность обязательна'),
});

export const step4Schema = Yup.object({
  hasOwnTravelPurpose: Yup.boolean(),
  travelPurposeDescription: Yup.string().when('hasOwnTravelPurpose', {
    is: true,
    then: (schema) => schema.required('Описание цели поездки обязательно'),
    otherwise: (schema) => schema,
  }),
  departureDate: Yup.date().when('hasOwnTravelPurpose', {
    is: true,
    then: (schema) => schema
      .min(new Date(), 'Дата вылета не может быть в прошлом')
      .required('Дата вылета обязательна'),
    otherwise: (schema) => schema,
  }),
  returnDate: Yup.date().when(['hasOwnTravelPurpose', 'departureDate'], {
    is: (hasOwnTravelPurpose: boolean, departureDate: Date) => hasOwnTravelPurpose && departureDate,
    then: (schema) => schema
      .min(Yup.ref('departureDate'), 'Дата возвращения должна быть позже даты вылета')
      .required('Дата возвращения обязательна'),
    otherwise: (schema) => schema,
  }),
  destination: Yup.string().when('hasOwnTravelPurpose', {
    is: true,
    then: (schema) => schema.required('Место назначения обязательно'),
    otherwise: (schema) => schema,
  }),
  hasInvitation: Yup.boolean(),
  invitationFile: Yup.string(),
  travelWithOthers: Yup.boolean(),
  travelAsGroup: Yup.boolean(),
  groupName: Yup.string(),
  companions: Yup.array(),
});

export const step5Schema = Yup.object({
  hasBeenToUSA: Yup.boolean().default(false),
  visitYear: Yup.number().when('hasBeenToUSA', {
    is: true,
    then: (schema) => schema.required('Укажите год поездки')
      .min(1900, 'Год должен быть не меньше 1900')
      .max(new Date().getFullYear(), 'Год не может быть в будущем'),
    otherwise: (schema) => schema,
  }),
  visitPurpose: Yup.string().when('hasBeenToUSA', {
    is: true,
    then: (schema) => schema.required('Укажите причину поездки'),
    otherwise: (schema) => schema,
  }),
  visitDuration: Yup.string().when('hasBeenToUSA', {
    is: true,
    then: (schema) => schema.required('Укажите длительность пребывания'),
    otherwise: (schema) => schema,
  }),
  visitDurationType: Yup.string().when('hasBeenToUSA', {
    is: true,
    then: (schema) => schema.required('Укажите тип длительности'),
    otherwise: (schema) => schema,
  }),
  visitVisaType: Yup.string(),
  hasUSVisa: Yup.boolean().default(false),
  lastVisaDate: Yup.date().nullable(),
  visaNumber: Yup.string(),
  isSameVisaType: Yup.boolean(),
  isSameCountry: Yup.boolean(),
  hasVisaRejections: Yup.boolean().default(false),
  rejectionVisaType: Yup.string(),
  rejectionDate: Yup.date().nullable(),
  visaRejections: Yup.array().of(
    Yup.object({
      visaType: Yup.string(),
      rejectionDate: Yup.date().nullable(),
      reason: Yup.string(),
    })
  ),
  hasPreviousDS160: Yup.boolean().default(false),
  previousDS160File: Yup.string(),
  ssn: Yup.string()
    .matches(/^\d{3}-\d{2}-\d{4}$/, 'SSN должен быть в формате XXX-XX-XXXX')
    .nullable(),
  taxId: Yup.string()
    .matches(/^\d{2}-\d{7}$/, 'Tax ID должен быть в формате XX-XXXXXXX')
    .nullable(),
  visaNumber: Yup.string()
    .when('hasUSVisa', {
      is: true,
      then: (schema) => schema
        .required('Номер визы обязателен')
        .min(8, 'Номер визы должен содержать минимум 8 символов'),
      otherwise: (schema) => schema.nullable(),
    }),
});

export const step6Schema = Yup.object({
  address: Yup.string().required('Адрес обязателен'),
  city: Yup.string().required('Город обязателен'),
  stateProvince: Yup.string(),
  country: Yup.string().required('Страна обязательна'),
  zipCode: Yup.string().required('Индекс обязателен'),
  phone: Yup.string()
    .test('phone-format', 'Формат: +7 XXX XXX XX XX', function(value) {
      if (!value) return false; // Required
      if (value === '+7' || value === '+7 ') return false; // Not just prefix
      return /^\+7 \d{3} \d{3} \d{2} \d{2}$/.test(value);
    })
    .required('Телефон обязателен'),
  email: Yup.string()
    .test('email-or-empty', 'Неверный формат email', function(value) {
      if (!value || value.trim() === '') return true;
      return Yup.string().email().isValidSync(value);
    }),
  socialMediaLinks: Yup.array()
    .of(Yup.string()),
});

export const step7Schema = Yup.object({
  hasSpouse: Yup.boolean(),
  spouseLastName: Yup.string().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required('Фамилия супруга(и) обязательна'),
    otherwise: (schema) => schema,
  }),
  spouseFirstName: Yup.string().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required('Имя супруга(и) обязательно'),
    otherwise: (schema) => schema,
  }),
  spouseMiddleName: Yup.string(),
  spouseCityOfBirth: Yup.string().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required('Город рождения супруга(и) обязателен'),
    otherwise: (schema) => schema,
  }),
  spouseCountryOfBirth: Yup.string().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required('Страна рождения супруга(и) обязательна'),
    otherwise: (schema) => schema,
  }),
  spouseDateOfBirth: Yup.date().when('hasSpouse', {
    is: true,
    then: (schema) => schema
      .required('Дата рождения супруга(и) обязательна')
      .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Супруг(а) должен(на) быть старше 18 лет')
      .min(new Date(Date.now() - 120 * 365 * 24 * 60 * 60 * 1000), 'Дата рождения супруга(и) не может быть более 120 лет назад'),
    otherwise: (schema) => schema,
  }),
  spouseCitizenship: Yup.string().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required('Гражданство супруга(и) обязательно'),
    otherwise: (schema) => schema,
  }),
  wasSpouseInUSA: Yup.boolean().when('hasSpouse', {
    is: true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  spouseUSAEntryDate: Yup.date().when(['hasSpouse', 'wasSpouseInUSA'], {
    is: (hasSpouse: boolean, wasSpouseInUSA: boolean) => hasSpouse && wasSpouseInUSA,
    then: (schema) => schema.required('Дата прибытия в США обязательна'),
    otherwise: (schema) => schema,
  }),
  spouseUSAStayDuration: Yup.string().when(['hasSpouse', 'wasSpouseInUSA'], {
    is: (hasSpouse: boolean, wasSpouseInUSA: boolean) => hasSpouse && wasSpouseInUSA,
    then: (schema) => schema.required('Продолжительность пребывания обязательна'),
    otherwise: (schema) => schema,
  }),
  spouseUSAStayDurationType: Yup.string().when(['hasSpouse', 'wasSpouseInUSA'], {
    is: (hasSpouse: boolean, wasSpouseInUSA: boolean) => hasSpouse && wasSpouseInUSA,
    then: (schema) => schema.required('Тип продолжительности обязателен'),
    otherwise: (schema) => schema,
  }),
  fatherSurname: Yup.string(),
  fatherName: Yup.string(),
  fatherDateOfBirth: Yup.date()
    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Отец должен быть старше 18 лет')
    .min(new Date(Date.now() - 120 * 365 * 24 * 60 * 60 * 1000), 'Дата рождения отца не может быть более 120 лет назад'),
  isFatherDateOfBirthUnknown: Yup.boolean(),
  isFatherInUSA: Yup.boolean(),
  fatherUSAReason: Yup.string(),
  motherSurname: Yup.string(),
  motherName: Yup.string(),
  motherDateOfBirth: Yup.date()
    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Мать должна быть старше 18 лет')
    .min(new Date(Date.now() - 120 * 365 * 24 * 60 * 60 * 1000), 'Дата рождения матери не может быть более 120 лет назад'),
  isMotherDateOfBirthUnknown: Yup.boolean(),
  isMotherInUSA: Yup.boolean(),
  motherUSAReason: Yup.string(),
  hasRelativesInUSA: Yup.boolean(),
  relatives: Yup.array(),
});

// Simplified Step 8 validation schema
export const step8Schema = Yup.object({
  // Basic required fields
  occupation: Yup.string()
    .oneOf([
      'agriculture', 'artist', 'business', 'communications', 'computer_science', 
      'culinary', 'education', 'engineering', 'government', 'homemaker', 
      'legal', 'medical', 'military', 'natural_science', 'not_employed', 
      'physical_science', 'religious', 'research', 'retired', 'social_science', 
      'student', 'other', 'employed', 'unemployed', 'business_owner', 
      'individual_entrepreneur', 'self_employed', 'freelancer'
    ])
    .required('Род занятий обязателен'),
  
  educationStatus: Yup.string()
    .required('Выберите ваш статус образования'),
  
  // Education location - required for students with no higher education
  educationLocation: Yup.string().when(['educationStatus', 'occupation'], {
    is: (status: string, occupation: string) => 
      occupation === 'student' && status === 'no_higher_education',
    then: (schema) => schema.required('Укажите где вы учитесь'),
    otherwise: (schema) => schema,
  }),

  // Institution name - required for completed/current higher education
  institutionName: Yup.string().when('educationStatus', {
    is: (status: string) => ['completed_education', 'current_student'].includes(status),
    then: (schema) => schema.required('Укажите учебное заведение'),
    otherwise: (schema) => schema,
  }),

  // Job info fields - simplified logic
  hasJob: Yup.boolean(),
  
  // Work fields - required when hasJob is true and not unemployed occupation (handle student case)
  companyName: Yup.string().when(['hasJob', 'occupation'], {
    is: (hasJob: boolean, occupation: string) => 
      hasJob && (occupation === 'student' || !['not_employed', 'retired', 'homemaker'].includes(occupation)),
    then: (schema) => schema.required('Название компании обязательно'),
    otherwise: (schema) => schema,
  }),
  
  workAddress: Yup.string().when(['hasJob', 'occupation'], {
    is: (hasJob: boolean, occupation: string) => 
      hasJob && (occupation === 'student' || !['not_employed', 'retired', 'homemaker'].includes(occupation)),
    then: (schema) => schema.required('Адрес работы обязателен'),
    otherwise: (schema) => schema,
  }),
  
  workExperience: Yup.string().when(['hasJob', 'occupation'], {
    is: (hasJob: boolean, occupation: string) => 
      hasJob && (occupation === 'student' || !['not_employed', 'retired', 'homemaker'].includes(occupation)),
    then: (schema) => schema.required('Стаж работы обязателен'),
    otherwise: (schema) => schema,
  }),
  
  workPhone: Yup.string().when(['hasJob', 'occupation'], {
    is: (hasJob: boolean, occupation: string) => 
      hasJob && (occupation === 'student' || !['not_employed', 'retired', 'homemaker'].includes(occupation)),
    then: (schema) => schema.required('Телефон работы обязателен'),
    otherwise: (schema) => schema,
  }),
  
  position: Yup.string().when(['hasJob', 'occupation'], {
    is: (hasJob: boolean, occupation: string) => 
      hasJob && (occupation === 'student' || !['not_employed', 'retired', 'homemaker'].includes(occupation)),
    then: (schema) => schema.required('Должность обязательна'),
    otherwise: (schema) => schema,
  }),

  // Optional fields that can be N/A
  workState: Yup.string(),
  workZipCode: Yup.string(),
  income: Yup.string(),
  institutionState: Yup.string(),
  institutionZipCode: Yup.string(),
  
  // N/A checkboxes
  workStateNA: Yup.boolean(),
  workZipCodeNA: Yup.boolean(),
  incomeNA: Yup.boolean(),
  institutionStateNA: Yup.boolean(),
  institutionZipCodeNA: Yup.boolean(),
  
  // Legacy fields (kept for backward compatibility)
  lastCompletedEducation: Yup.string(),
  otherEducation: Yup.string(),
  isCurrentStudent: Yup.boolean(),
  universityName: Yup.string(),
  universityAddress: Yup.string(),
  faculty: Yup.string(),
  startDate: Yup.date(),
  endDate: Yup.date(),
});

export const step9Schema = Yup.object({
  visitedCountries: Yup.array()
    .test('required-or-not-traveled', 'Укажите хотя бы одну страну или выберите "Не был за границей"', function(value) {
      const hasNotTraveled = this.parent.hasNotTraveled;
      return hasNotTraveled || (value && value.length > 0);
    })
    .required('Список стран обязателен'),
  hasNotTraveled: Yup.boolean(),
});