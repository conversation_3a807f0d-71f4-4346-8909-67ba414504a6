import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray, FormikProps } from 'formik';
import { step5Schema } from '../utils/validations';
import { Step5Data } from '../utils/types';

interface Step5Props {
  initialValues: Step5Data;
  onSubmit: (values: Step5Data) => void;
}

export interface Step5Ref {
  submitForm: () => void;
  isValid: boolean;
}

const Step5_VisaHistory = forwardRef<Step5Ref, Step5Props>(({ initialValues, onSubmit }, ref) => {
  const formikRef = useRef<FormikProps<Step5Data>>(null);
  const formRef = useRef<FormikProps<Step5Data> | null>(null);
  const [forceUpdate, setForceUpdate] = useState(0);


  // Add a function to handle form submission directly
  const handleFormSubmit = () => {
    if (formikRef.current) {
      // Get current values
      const values = formikRef.current.values;
      
      // Ensure boolean fields are properly set
      if (values.hasBeenToUSA === undefined) {
        values.hasBeenToUSA = false;
      }
      
      if (values.hasUSVisa === undefined) {
        values.hasUSVisa = false;
      }
      
      if (values.hasVisaRejections === undefined) {
        values.hasVisaRejections = false;
      }
      
      if (values.hasPreviousDS160 === undefined) {
        values.hasPreviousDS160 = false;
      }
      
      // Submit the form with current values
      onSubmit(values);
    }
  };

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log('[Step5 Submit] submitForm called');
      handleFormSubmit();
    },
    get isValid() {
      const formikValid = formikRef.current?.isValid ?? false;
      console.log('[Step5 isValid] Real-time check:', {
        formikValid,
        formikValues: formikRef.current?.values,
        formikErrors: formikRef.current?.errors,
      });
      return formikValid;
    }
  }), [forceUpdate]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Поездки в США</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте информацию о ваших предыдущих поездках в США, если таковые имеются.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={step5Schema}
        validateOnMount={true}
        validateOnChange={true}
        validateOnBlur={true}
        onSubmit={(values) => {
          onSubmit(values);
        }}
      >
        {(formikProps) => {
          formRef.current = formikProps;
          
          // Принудительно обновляем useImperativeHandle при изменении Formik
          useEffect(() => {
            console.log('[Step5 Formik] State changed:', {
              formikIsValid: formikProps.isValid,
              values: formikProps.values,
              errors: formikProps.errors,
            });
            setForceUpdate(prev => prev + 1);
          }, [formikProps.isValid, formikProps.values]);

          return (
            <Form>
              <div className="p-4 border border-gray-200 rounded-lg mb-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Предыдущая анкета DS-160</h3>
                <p className="text-gray-600 mb-4">
                  Есть ли у вас анкета от другой визовой компании? Загрузите её
                </p>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasPreviousDS160"
                      name="hasPreviousDS160"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        formikProps.setFieldValue('hasPreviousDS160', e.target.checked);
                        if (!e.target.checked) {
                          formikProps.setFieldValue('previousDS160File', '');
                        }
                      }}
                    />
                    <label htmlFor="hasPreviousDS160" className="ml-2 block text-gray-700 font-medium">
                      У меня есть предыдущая анкета DS-160
                    </label>
                  </div>
                </div>

                {formikProps.values.hasPreviousDS160 && (
                  <div className="mt-2">
                    <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center">
                      <p className="text-gray-600 mb-2">Загрузите файл предыдущей анкеты</p>
                      <input
                        type="file"
                        id="previousDS160File"
                        name="previousDS160File"
                        className="hidden"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            formikProps.setFieldValue('previousDS160File', file.name);
                          }
                        }}
                      />
                      <label
                        htmlFor="previousDS160File"
                        className="bg-blue-500 text-white px-3 py-1 rounded cursor-pointer inline-block"
                      >
                        Выбрать файл
                      </label>
                      {formikProps.values.previousDS160File && (
                        <p className="text-green-600 mt-2">
                          Файл загружен: {formikProps.values.previousDS160File}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasBeenToUSA"
                      name="hasBeenToUSA"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        formikProps.setFieldValue('hasBeenToUSA', e.target.checked);
                        if (!e.target.checked) {
                          // Clear all related fields when unchecked
                          formikProps.setFieldValue('visitYear', '');
                          formikProps.setFieldValue('visitPurpose', '');
                          formikProps.setFieldValue('visitDuration', '');
                          formikProps.setFieldValue('visitDurationType', 'days');
                          formikProps.setFieldValue('visitVisaType', '');
                          formikProps.setFieldValue('hasSSN', false);
                          formikProps.setFieldValue('ssn', '');
                          formikProps.setFieldValue('hasTaxpayerId', false);
                          formikProps.setFieldValue('taxpayerId', '');
                        }
                      }}
                    />
                    <label htmlFor="hasBeenToUSA" className="ml-2 block text-gray-700 font-medium">
                      Были ли вы в США ранее?
                    </label>
                  </div>
                </div>

                {formikProps.values.hasBeenToUSA && (
                  <div className="p-4 border border-gray-200 rounded-lg mb-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-4">Информация о прошлых посещениях США</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="visitYear" className="block text-gray-700 font-medium mb-2">
                          Год поездки
                        </label>
                        <Field
                          type="number"
                          id="visitYear"
                          name="visitYear"
                          min="1900"
                          max={new Date().getFullYear()}
                          className="form-input"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="visitPurpose" className="block text-gray-700 font-medium mb-2">
                          Причина поездки
                        </label>
                        <Field
                          type="text"
                          id="visitPurpose"
                          name="visitPurpose"
                          className="form-input"
                          placeholder="Например: туризм, учеба, бизнес"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="mb-4">
                        <label htmlFor="visitDuration" className="block text-gray-700 font-medium mb-2">
                          Длительность пребывания
                        </label>
                        <div className="flex items-center">
                          <Field
                            type="number"
                            id="visitDuration"
                            name="visitDuration"
                            min="1"
                            className="form-input w-2/3"
                          />
                          <Field
                            as="select"
                            id="visitDurationType"
                            name="visitDurationType"
                            className="form-input w-1/3 ml-2"
                          >
                            <option value="days">дней</option>
                            <option value="weeks">недель</option>
                            <option value="months">месяцев</option>
                            <option value="years">лет</option>
                          </Field>
                        </div>
                      </div>

                      <div className="mb-4">
                        <label htmlFor="visitVisaType" className="block text-gray-700 font-medium mb-2">
                          Тип визы (если была)
                        </label>
                        <Field
                          type="text"
                          id="visitVisaType"
                          name="visitVisaType"
                          className="form-input"
                          placeholder="Например: B1/B2, F1, и т.д."
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <Field
                          type="checkbox"
                          id="hasSSN"
                          name="hasSSN"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            formikProps.setFieldValue('hasSSN', e.target.checked);
                            if (!e.target.checked) {
                              formikProps.setFieldValue('ssn', '');
                            }
                          }}
                        />
                        <label htmlFor="hasSSN" className="ml-2 block text-gray-700 font-medium">
                          Имеется ли у вас номер социального страхования США (SSN)?
                        </label>
                      </div>
                      {formikProps.values.hasSSN && (
                        <div className="mt-2">
                          <Field
                            type="text"
                            id="ssn"
                            name="ssn"
                            placeholder="Введите SSN"
                            className="form-input"
                          />
                          <ErrorMessage
                            name="ssn"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      )}
                    </div>

                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <Field
                          type="checkbox"
                          id="hasTaxpayerId"
                          name="hasTaxpayerId"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            formikProps.setFieldValue('hasTaxpayerId', e.target.checked);
                            if (!e.target.checked) {
                              formikProps.setFieldValue('taxpayerId', '');
                            }
                          }}
                        />
                        <label htmlFor="hasTaxpayerId" className="ml-2 block text-gray-700 font-medium">
                          Имеется ли у вас U.S. Taxpayer ID Number?
                        </label>
                      </div>
                      {formikProps.values.hasTaxpayerId && (
                        <div className="mt-2">
                          <Field
                            type="text"
                            id="taxpayerId"
                            name="taxpayerId"
                            placeholder="Введите Taxpayer ID"
                            className="form-input"
                          />
                          <ErrorMessage
                            name="taxpayerId"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasUSVisa"
                      name="hasUSVisa"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        formikProps.setFieldValue('hasUSVisa', e.target.checked);
                        if (!e.target.checked) {
                          formikProps.setFieldValue('lastVisaDate', '');
                          formikProps.setFieldValue('visaNumber', '');
                          formikProps.setFieldValue('isSameVisaType', false);
                          formikProps.setFieldValue('isSameCountry', false);
                        }
                      }}
                    />
                    <label htmlFor="hasUSVisa" className="ml-2 block text-gray-700 font-medium">
                      Имели ли вы ранее визу США?
                    </label>
                  </div>

                  {formikProps.values.hasUSVisa && (
                    <div className="mt-4 mb-4 p-4 border border-gray-200 rounded-md">
                      <div className="mb-4">
                        <label htmlFor="lastVisaDate" className="block text-gray-700 font-medium mb-2">
                          Дата последней визы
                        </label>
                        <Field
                          type="date"
                          id="lastVisaDate"
                          name="lastVisaDate"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="lastVisaDate"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="visaNumber" className="block text-gray-700 font-medium mb-2">
                          Номер визы
                        </label>
                        <Field
                          type="text"
                          id="visaNumber"
                          name="visaNumber"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="visaNumber"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <Field
                            type="checkbox"
                            id="isSameVisaType"
                            name="isSameVisaType"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              formikProps.setFieldValue('isSameVisaType', e.target.checked);
                            }}
                          />
                          <label htmlFor="isSameVisaType" className="ml-2 block text-gray-700 font-medium">
                            Подаете ли вы на такой же тип визы?
                          </label>
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <Field
                            type="checkbox"
                            id="isSameCountry"
                            name="isSameCountry"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              formikProps.setFieldValue('isSameCountry', e.target.checked);
                            }}
                          />
                          <label htmlFor="isSameCountry" className="ml-2 block text-gray-700 font-medium">
                            Подаете ли вы с той же страны, где получали визу?
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasVisaRejections"
                      name="hasVisaRejections"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        formikProps.setFieldValue('hasVisaRejections', e.target.checked);
                        if (!e.target.checked) {
                          formikProps.setFieldValue('visaRejections', []);
                          formikProps.setFieldValue('rejectionVisaType', '');
                          formikProps.setFieldValue('rejectionDate', '');
                        }
                      }}
                    />
                    <label htmlFor="hasVisaRejections" className="ml-2 block text-gray-700 font-medium">
                      Были ли отказы в визе США?
                    </label>
                  </div>

                  {formikProps.values.hasVisaRejections && (
                    <div className="mt-4 mb-4 p-4 border border-gray-200 rounded-md">
                      <h4 className="text-lg font-medium text-gray-800 mb-4">Отказы в визе США</h4>

                      {formikProps.values.rejectionVisaType && formikProps.values.rejectionDate && (
                        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                          <p className="text-sm text-yellow-800 mb-2">Найдены данные из предыдущей версии:</p>
                          <p className="text-sm"><strong>Тип визы:</strong> {formikProps.values.rejectionVisaType}</p>
                          <p className="text-sm"><strong>Дата отказа:</strong> {formikProps.values.rejectionDate}</p>
                          <button
                            type="button"
                            className="mt-2 text-sm bg-blue-500 text-white px-3 py-1 rounded"
                            onClick={() => {
                              const newRejection = {
                                visaType: formikProps.values.rejectionVisaType || '',
                                rejectionDate: formikProps.values.rejectionDate || '',
                                reason: ''
                              };
                              const currentRejections = formikProps.values.visaRejections || [];
                              formikProps.setFieldValue('visaRejections', [...currentRejections, newRejection]);
                              formikProps.setFieldValue('rejectionVisaType', '');
                              formikProps.setFieldValue('rejectionDate', '');
                            }}
                          >
                            Перенести в новый формат
                          </button>
                        </div>
                      )}

                      <FieldArray name="visaRejections">
                        {({ push, remove }) => (
                          <div>
                            {formikProps.values.visaRejections && formikProps.values.visaRejections.length > 0 ? (
                              formikProps.values.visaRejections.map((rejection, index) => (
                                <div key={index} className="mb-4 p-4 border border-gray-300 rounded-md">
                                  <div className="flex justify-between items-center mb-3">
                                    <h5 className="font-medium text-gray-700">Отказ #{index + 1}</h5>
                                    <button
                                      type="button"
                                      className="text-red-500 hover:text-red-700"
                                      onClick={() => remove(index)}
                                    >
                                      Удалить
                                    </button>
                                  </div>

                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                      <label htmlFor={`visaRejections.${index}.visaType`} className="block text-gray-700 font-medium mb-2">
                                        Тип визы
                                      </label>
                                      <Field
                                        type="text"
                                        id={`visaRejections.${index}.visaType`}
                                        name={`visaRejections.${index}.visaType`}
                                        className="form-input"
                                        placeholder="B1/B2, F1, и т.д."
                                      />
                                      <ErrorMessage
                                        name={`visaRejections.${index}.visaType`}
                                        component="div"
                                        className="text-red-500 text-sm mt-1"
                                      />
                                    </div>

                                    <div>
                                      <label htmlFor={`visaRejections.${index}.rejectionDate`} className="block text-gray-700 font-medium mb-2">
                                        Дата отказа
                                      </label>
                                      <Field
                                        type="date"
                                        id={`visaRejections.${index}.rejectionDate`}
                                        name={`visaRejections.${index}.rejectionDate`}
                                        className="form-input"
                                      />
                                      <ErrorMessage
                                        name={`visaRejections.${index}.rejectionDate`}
                                        component="div"
                                        className="text-red-500 text-sm mt-1"
                                      />
                                    </div>
                                  </div>

                                  <div className="mt-3">
                                    <label htmlFor={`visaRejections.${index}.reason`} className="block text-gray-700 font-medium mb-2">
                                      Причина отказа (необязательно)
                                    </label>
                                    <Field
                                      as="textarea"
                                      id={`visaRejections.${index}.reason`}
                                      name={`visaRejections.${index}.reason`}
                                      className="form-input"
                                      rows="2"
                                      placeholder="Укажите причину отказа, если известна"
                                    />
                                  </div>
                                </div>
                              ))
                            ) : (
                              <div className="text-gray-500 mb-4">Нет добавленных отказов в визе</div>
                            )}

                            <button
                              type="button"
                              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                              onClick={() => push({ visaType: '', rejectionDate: '', reason: '' })}
                            >
                              Добавить отказ в визе
                            </button>
                          </div>
                        )}
                      </FieldArray>

                      <div style={{ display: 'none' }}>
                        <Field
                          type="text"
                          name="rejectionVisaType"
                        />
                        <Field
                          type="date"
                          name="rejectionDate"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step5_VisaHistory.displayName = 'Step5_VisaHistory';

export default Step5_VisaHistory;