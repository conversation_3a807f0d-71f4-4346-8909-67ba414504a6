import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage, FormikProps } from 'formik';
import { step2Schema } from '../utils/validations';
import FileUpload from './FileUpload';
import { ExtractedDocumentData } from '../utils/ocr';
import { Step2Data } from '../utils/types';
import { COUNTRIES } from '../constants/countries';
import { useAutocompleteDetection } from '../hooks/useAutocompleteDetection';

export interface Step2Ref {
  submitForm: () => void;
  isValid: boolean;
}

interface Step2Props {
  initialValues: Step2Data;
  onSubmit: (values: Step2Data) => void;
  uploadedFiles: { [key: string]: string };
  setUploadedFiles: (files: { [key: string]: string }) => void;
  onFormDataUpdate: (data: Partial<Step2Data>) => void;
}

const Step2_DocumentUpload = forwardRef<Step2Ref, Step2Props>(({
  initialValues,
  onSubmit,
  uploadedFiles,
  setUploadedFiles,
  onFormDataUpdate,
}, ref) => {
  const formikRef = useRef<FormikProps<Step2Data>>(null);
  const [isDocumentUploaded, setIsDocumentUploaded] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [formValues, setFormValues] = useState<Step2Data>(initialValues);
  const setFieldValueRef = useRef<((field: string, value: any) => void) | null>(null);

  // Fields that are commonly autocompleted
  const autocompleteFields = ['surname', 'name', 'passportNumber', 'iin', 'idNumber'];
  
  // Use autocomplete detection hook
  useAutocompleteDetection(formikRef, autocompleteFields);

  useEffect(() => {
    // Check if at least one document is uploaded
    const hasUploads = Object.keys(uploadedFiles).length > 0;
    setIsDocumentUploaded(hasUploads);
  }, [uploadedFiles]);

  const isStepValid = isFormValid && isDocumentUploaded;
  
  // Log step validity changes
  useEffect(() => {
    console.log('Step2: Step validity changed:', {
      isStepValid,
      isFormValid,
      isDocumentUploaded,
      uploadedFilesCount: Object.keys(uploadedFiles).length
    });
  }, [isStepValid, isFormValid, isDocumentUploaded, uploadedFiles]);

  useImperativeHandle(ref, () => {
    return {
      submitForm: () => {
        if (formikRef.current) {
          formikRef.current.submitForm();
        }
      },
      isValid: isStepValid,
    };
  }, [isStepValid]);

  const handleDocumentExtract = (documentType: string, data: ExtractedDocumentData, filePath?: string) => {
    console.log('Step2: Document extracted:', { documentType, data, filePath });
    
    // Update Formik form values using the ref
    if (setFieldValueRef.current) {
      console.log('Step2: Updating Formik fields with extracted data');
      Object.entries(data).forEach(([key, value]) => {
        setFieldValueRef.current!(key, value);
      });
      
      // Trigger validation after updating fields
      setTimeout(() => {
        if (formikRef.current) {
          console.log('Step2: Triggering validation after field updates');
          formikRef.current.validateForm();
        }
      }, 100);
    } else {
      console.log('Step2: Formik ref not available, using fallback local state');
      // Fallback to local state if Formik ref not available yet
      const newFormValues = {
        ...formValues,
        ...data,
      };
      setFormValues(newFormValues);
    }

    // Track the uploaded file with its actual path from Supabase storage
    const newUploadedFiles = {
      ...uploadedFiles,
      [documentType]: filePath || 'uploaded',
    };
    console.log('Step2: Updating uploaded files:', newUploadedFiles);
    setUploadedFiles(newUploadedFiles);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Загрузите документы</h2>
      <p className="text-gray-600 mb-4">
        Пожалуйста, загрузите скан или фото вашего паспорта и удостоверения личности.
        Мы автоматически извлечем данные, которые вы сможете проверить и отредактировать.
      </p>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Требования для продолжения</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Заполнить все обязательные поля (*)</li>
              <li>• Указать хотя бы один идентификатор: номер паспорта, ИИН или номер удостоверения</li>
              <li>• Загрузить хотя бы один документ (паспорт или удостоверение)</li>
              <li>• Можно указать несколько идентификаторов для более полной информации</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <FileUpload
          label="Загрузите скан/фото паспорта"
          name="passport"
          onExtract={(data, filePath) => handleDocumentExtract('passport', data, filePath)}
        />

        <FileUpload
          label="Загрузите скан/фото удостоверения личности"
          name="idCard"
          onExtract={(data, filePath) => handleDocumentExtract('idCard', data, filePath)}
        />
      </div>

      {!isDocumentUploaded && (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700 font-medium">
                Совет: Загрузите документ для автоматического заполнения
              </p>
              <p className="text-sm text-blue-600 mt-1">
                Загрузите паспорт или удостоверение личности, чтобы мы автоматически извлекли данные. Или заполните поля вручную.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-800 mb-4">
          Проверьте и отредактируйте данные
        </h3>

        <Formik
          innerRef={formikRef}
          initialValues={initialValues}
          validationSchema={step2Schema}
          onSubmit={(values) => {
            console.log('Step2 Form submission attempted:', {
              values,
              isDocumentUploaded,
              uploadedFiles,
              isFormValid
            });
            
            if (isDocumentUploaded) {
              console.log('Step2: Submitting form with values:', values);
              onSubmit(values);
            } else {
              console.warn('Step2: Form submission blocked - no documents uploaded');
              alert('Пожалуйста, загрузите хотя бы один документ для продолжения');
            }
          }}
          enableReinitialize={true}
          validateOnMount={false}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({ isValid: formikIsValid, errors, touched, values, setFieldValue }) => {
            // Store setFieldValue in ref for access outside render function
            setFieldValueRef.current = setFieldValue;

            // Update form validity state when Formik validation changes
            useEffect(() => {
              console.log('Step2: Formik validation state changed:', formikIsValid);
              setIsFormValid(formikIsValid);
            }, [formikIsValid]);

            // Update local form values when Formik values change
            useEffect(() => {
              setFormValues(values);
            }, [values]);

            return (
            <Form>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <label htmlFor="surname" className="block text-gray-700 font-medium mb-2">
                    Фамилия (Surname) * <span className="text-sm text-gray-500">только латиница</span>
                  </label>
                  <Field
                    type="text"
                    id="surname"
                    name="surname"
                    className="form-input"
                    placeholder="Ivanov"
                  />
                  {touched.surname && errors.surname && (
                    <div className="text-red-500 text-sm mt-1">{errors.surname}</div>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                    Имя (Name) * <span className="text-sm text-gray-500">только латиница</span>
                  </label>
                  <Field
                    type="text"
                    id="name"
                    name="name"
                    className="form-input"
                    placeholder="Ivan"
                  />
                  {touched.name && errors.name && (
                    <div className="text-red-500 text-sm mt-1">{errors.name}</div>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="dateOfBirth" className="block text-gray-700 font-medium mb-2">
                    Дата рождения <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    className="form-input"
                    min={(new Date().getFullYear() - 120) + "-01-01"}
                    max={(new Date().getFullYear() - 18) + "-12-31"}
                  />
                  <ErrorMessage
                    name="dateOfBirth"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="citizenship" className="block text-gray-700 font-medium mb-2">
                    Гражданство *
                  </label>
                  <Field
                    as="select"
                    id="citizenship"
                    name="citizenship"
                    className="form-input"
                  >
                    <option value="">Выберите страну...</option>
                    {COUNTRIES.map((country) => (
                      <option key={country} value={country}>
                        {country}
                      </option>
                    ))}
                  </Field>
                  <ErrorMessage
                    name="citizenship"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportNumber" className="block text-gray-700 font-medium mb-2">
                    Номер паспорта <span className="text-gray-500 text-sm">(один из трех идентификаторов)</span>
                  </label>
                  <Field
                    type="text"
                    id="passportNumber"
                    name="passportNumber"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportNumber"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportIssueDate" className="block text-gray-700 font-medium mb-2">
                    Дата выдачи паспорта <span className="text-gray-500 text-sm">(если указан номер паспорта)</span>
                  </label>
                  <Field
                    type="date"
                    id="passportIssueDate"
                    name="passportIssueDate"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportIssueDate"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="passportExpiryDate" className="block text-gray-700 font-medium mb-2">
                    Дата окончания паспорта <span className="text-gray-500 text-sm">(если указан номер паспорта)</span>
                  </label>
                  <Field
                    type="date"
                    id="passportExpiryDate"
                    name="passportExpiryDate"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="passportExpiryDate"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="iin" className="block text-gray-700 font-medium mb-2">
                    ИИН <span className="text-gray-500 text-sm">(12 цифр, один из трех идентификаторов)</span>
                  </label>
                  <Field
                    type="text"
                    id="iin"
                    name="iin"
                    className="form-input"
                    placeholder="012345678901"
                  />
                  <ErrorMessage
                    name="iin"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="idNumber" className="block text-gray-700 font-medium mb-2">
                    Номер удостоверения личности <span className="text-gray-500 text-sm">(9 цифр, один из трех идентификаторов)</span>
                  </label>
                  <Field
                    type="text"
                    id="idNumber"
                    name="idNumber"
                    className="form-input"
                    placeholder="123456789"
                  />
                  {touched.idNumber && errors.idNumber && (
                    <div className="text-red-500 text-sm mt-1">{errors.idNumber}</div>
                  )}
                </div>
              </div>

              {/* Hidden submit button - will be triggered by StepWrapper */}
              <button type="submit" style={{ display: 'none' }} />
            </Form>
            );
          }}
        </Formik>
      </div>
    </div>
  );
});

Step2_DocumentUpload.displayName = 'Step2_DocumentUpload';

export default Step2_DocumentUpload;