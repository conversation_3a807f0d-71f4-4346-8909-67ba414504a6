import React, { forwardRef, useImperativeHandle, useRef, useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray, FormikProps, FieldProps } from 'formik';
// Custom phone mask implementation for React 19 compatibility
import { step6Schema } from '../utils/validations';
import { Step6Data } from '../utils/types';
import { COUNTRIES } from '../constants/countries';
import { sanitizeUrl, sanitizeTextInput, sanitizeAddress } from '../utils/sanitization';
import { formatPhoneInput, isValidKazakhstanPhone } from '../utils/validations';

export interface Step6Ref {
  submitForm: () => void;
  isValid: boolean;
}

interface Step6Props {
  initialValues: Step6Data;
  onSubmit: (values: Step6Data) => void;
}

const Step6_ContactInfo = forwardRef<Step6Ref, Step6Props>(({ initialValues, onSubmit }, ref) => {
  const formikRef = useRef<FormikProps<Step6Data>>(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      console.log('[Step6 Submit] submitForm called');
      formikRef.current?.submitForm();
    },
    get isValid() {
      const formikValid = formikRef.current?.isValid ?? false;
      console.log('[Step6 isValid] Real-time check:', {
        formikValid,
        formikValues: formikRef.current?.values,
        formikErrors: formikRef.current?.errors,
      });
      return formikValid;
    }
  }), [forceUpdate]);



  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Контактная информация</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте вашу контактную информацию и ссылки на социальные сети.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={step6Schema}
        onSubmit={onSubmit}
        validateOnChange={true}
        validateOnBlur={true}
        validateOnMount={false}
        enableReinitialize={true}
      >
        {({ values, isValid: formikIsValid, setFieldValue }) => {
          // Принудительно обновляем useImperativeHandle при изменении Formik
          useEffect(() => {
            console.log('[Step6 Formik] State changed:', {
              formikIsValid,
              values,
              errors: formikRef.current?.errors,
            });
            setForceUpdate(prev => prev + 1);
          }, [formikIsValid, values]);

          return (
          <Form>
            <div className="grid grid-cols-1 gap-4">

              <div className="mb-4">
                <label htmlFor="address" className="block text-gray-700 font-medium mb-2">
                  Домашний адрес (улица, дом) <span className="text-red-500">*</span>
                </label>
                <Field
                  type="text"
                  id="address"
                  name="address"
                  className="form-input"
                  placeholder="Укажите ваш домашний адрес"
                />
                <ErrorMessage
                  name="address"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <label htmlFor="city" className="block text-gray-700 font-medium mb-2">
                    Город <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="text"
                    id="city"
                    name="city"
                    className="form-input"
                    placeholder="Укажите ваш город"
                  />
                  <ErrorMessage
                    name="city"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="stateProvince" className="block text-gray-700 font-medium mb-2">
                    Штат/Область/Провинция
                  </label>
                  <Field
                    type="text"
                    id="stateProvince"
                    name="stateProvince"
                    className="form-input"
                    placeholder="Область или регион"
                  />
                  <ErrorMessage
                    name="stateProvince"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="country" className="block text-gray-700 font-medium mb-2">
                  Страна <span className="text-red-500">*</span>
                </label>
                <Field
                  as="select"
                  id="country"
                  name="country"
                  className="form-input"
                >
                  <option value="">Выберите страну...</option>
                  {COUNTRIES.map((country) => (
                    <option key={country} value={country}>
                      {country}
                    </option>
                  ))}
                </Field>
                <ErrorMessage
                  name="country"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="zipCode" className="block text-gray-700 font-medium mb-2">
                  Почтовый индекс <span className="text-red-500">*</span>
                </label>
                <Field
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  className="form-input"
                  placeholder="Почтовый индекс"
                />
                <ErrorMessage
                  name="zipCode"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                  Телефон <span className="text-red-500">*</span>
                </label>
                <Field name="phone">
                  {({ field, form }: FieldProps) => {
                    // Use centralized phone formatting function

                    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                      const formatted = formatPhoneInput(e.target.value);
                      form.setFieldValue('phone', formatted);
                    };

                    return (
                      <input
                        {...field}
                        type="tel"
                        className="form-input"
                        placeholder="+7 ___ ___ __ __"
                        value={field.value || '+7'}
                        onChange={handlePhoneChange}
                        onFocus={(e) => {
                          if (!e.target.value || e.target.value === '') {
                            form.setFieldValue('phone', '+7 ');
                          }
                        }}
                      />
                    );
                  }}
                </Field>
                <ErrorMessage
                  name="phone"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
                <div className="text-sm text-gray-500 mt-1">
                  Формат: +7 XXX XXX XX XX (казахстанский номер)
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                  Email
                </label>
                <Field
                  type="email"
                  id="email"
                  name="email"
                  className="form-input"
                  placeholder="<EMAIL>"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
                <div className="text-sm text-gray-500 mt-1">
                  Email не обязателен, но если указан, должен быть корректным
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">
                  Ссылки на социальные сети (желательно минимум одна)
                </label>
                <FieldArray name="socialMediaLinks">
                  {({ push, remove }) => (
                    <div>
                      {values.socialMediaLinks && values.socialMediaLinks.length > 0 ? (
                        values.socialMediaLinks.map((link, index) => (
                          <div key={index} className="flex items-center gap-2 mb-2">
                            <Field
                              type="text"
                              name={`socialMediaLinks.${index}`}
                              className="form-input flex-1"
                              placeholder="https://instagram.com/username или https://facebook.com/profile"
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const sanitizedValue = sanitizeUrl(e.target.value);
                                setFieldValue(`socialMediaLinks.${index}`, sanitizedValue);
                              }}
                            />
                            <button
                              type="button"
                              className="bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600"
                              onClick={() => remove(index)}
                            >
                              Удалить
                            </button>
                          </div>
                        ))
                      ) : (
                        <div className="text-gray-500 mb-2">Нет добавленных ссылок</div>
                      )}
                      <button
                        type="button"
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                        onClick={() => push('')}
                      >
                        Добавить ссылку
                      </button>
                    </div>
                  )}
                </FieldArray>
                {values.socialMediaLinks && values.socialMediaLinks.length === 0 && (
                  <ErrorMessage
                    name="socialMediaLinks"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                )}
                <div className="text-sm text-gray-500 mt-2">
                  Укажите ссылки на ваши профили в социальных сетях (Instagram, Facebook, LinkedIn и т.д.)
                </div>
              </div>
            </div>

            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
          );
        }}
      </Formik>
    </div>
  );
});

Step6_ContactInfo.displayName = 'Step6_ContactInfo';

export default Step6_ContactInfo;