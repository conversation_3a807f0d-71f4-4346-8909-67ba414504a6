import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { ExtractedDocumentData } from '../utils/ocr';
import Cookies from 'js-cookie';
import { sanitizeFileName } from '../utils/sanitization';

interface FileUploadProps {
  onExtract: (data: ExtractedDocumentData, filePath?: string) => void;
  label: string;
  name: string;
  acceptedFileTypes?: string;
  maxSizeMB?: number;
  existingFile?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onExtract,
  label,
  name,
  maxSizeMB = 10,
  existingFile,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [hasExistingFile, setHasExistingFile] = useState(false);

  // Check if there's an existing file from database
  React.useEffect(() => {
    if (existingFile && existingFile !== 'uploaded') {
      setHasExistingFile(true);
      console.log(`FileUpload: Found existing file for ${name}:`, existingFile);
    } else {
      setHasExistingFile(false);
    }
  }, [existingFile, name]);

  // onDrop callback for handling file uploads
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (!file) {
        setError('Не выбран файл для загрузки');
        return;
      }

      // Validate file before uploading
      const validateFile = (file: File): string | null => {
        // Sanitize file name
        const sanitizedName = sanitizeFileName(file.name);
        if (!sanitizedName || sanitizedName.length === 0) {
          return 'Недопустимое имя файла';
        }

        // Log file information for debugging
        console.log('File info:', {
          name: file.name,
          sanitizedName,
          type: file.type,
          size: `${(file.size / (1024 * 1024)).toFixed(2)} MB`,
        });

        // Check minimum file size (prevent empty files)
        if (file.size < 100) {
          return 'Файл слишком маленький (минимум 100 байт)';
        }

        // Check file size (convert MB to bytes)
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
          return `Файл слишком большой. Максимальный размер: ${maxSizeMB}MB`;
        }

        // For empty or incorrect MIME types, try to detect from extension
        let fileType = file.type;
        if (!fileType || fileType === 'application/octet-stream') {
          const extension = file.name.split('.').pop()?.toLowerCase();
          if (extension === 'jpg' || extension === 'jpeg') {
            fileType = 'image/jpeg';
          } else if (extension === 'png') {
            fileType = 'image/png';
          } else if (extension === 'pdf') {
            fileType = 'application/pdf';
          } else if (extension === 'heic' || extension === 'heif') {
            fileType = 'image/heic';
          } else if (extension === 'bmp') {
            fileType = 'image/bmp';
          }
        }

        // Validate file type more permissively
        const validImageTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/bmp', 'image/heic'];
        const validPdfTypes = ['application/pdf'];

        if (!validImageTypes.includes(fileType) && !validPdfTypes.includes(fileType)) {
          return `Неподдерживаемый тип файла (${fileType}). Разрешены: JPG, PNG, PDF`;
        }

        // Additional security checks
        const fileName = file.name.toLowerCase();
        
        // Check for potentially dangerous file extensions
        const dangerousExtensions = ['.exe', '.bat', '.com', '.cmd', '.scr', '.pif', '.jar', '.js', '.vbs', '.ps1', '.sh'];
        if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
          return 'Недопустимый тип файла';
        }

        // Check for double extensions (e.g., file.pdf.exe)
        const parts = fileName.split('.');
        if (parts.length > 2) {
          const secondLastExt = `.${parts[parts.length - 2]}`;
          if (dangerousExtensions.includes(secondLastExt)) {
            return 'Недопустимый тип файла';
          }
        }

        return null; // Valid file type
      };

      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      setIsUploading(true);
      setError(null);
      setUploadedFile(file);

      try {
        // Create a new FormData instance
        const formData = new FormData();

        // Ensure the file has a proper name
        const fileName = file.name || `document.${file.type.split('/')[1] || 'jpg'}`;

        // Create a new File object with a guaranteed name if needed
        const fileToUpload = file.name ? file : new File(
          [file],
          fileName,
          { type: file.type || 'image/jpeg' }
        );

        // Log what we're sending
        console.log('Sending to server:', {
          fieldName: name,
          fileName: fileToUpload.name,
          fileType: fileToUpload.type,
          fileSize: `${(fileToUpload.size / (1024 * 1024)).toFixed(2)} MB`
        });

        // Add file to form data with field name
        formData.append(name, fileToUpload);

        // Get agent ID from localStorage or cookies
        const getAgentId = () => {
          // First try localStorage
          const localStorageAgentId = localStorage.getItem('agentId');
          if (localStorageAgentId) {
            return localStorageAgentId;
          }

          // Then try cookies (for backward compatibility)
          const cookieAgentId = Cookies.get('agentId');
          if (cookieAgentId) {
            // Migrate to localStorage
            localStorage.setItem('agentId', cookieAgentId);
            return cookieAgentId;
          }

          return null;
        };

        const agentId = getAgentId();

        // Build URL with proper encoding for query parameters
        const baseUrl = '/api/ocr';
        const url = agentId ? `${baseUrl}?agentId=${encodeURIComponent(agentId)}` : baseUrl;

        console.log('Sending file to server:', {
          fileName: fileToUpload.name,
          fileSize: `${(fileToUpload.size / (1024 * 1024)).toFixed(2)} MB`,
          fileType: fileToUpload.type,
          url: url,
          agentId: agentId
        });

        // Add timeout to the fetch request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          console.log('FileUpload: Request timeout, aborting...');
          controller.abort();
        }, 180000); // 3 minute timeout

        let response;
        try {
          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
            },
            body: formData,
            signal: controller.signal,
          });
          clearTimeout(timeoutId);
        } catch (fetchError) {
          clearTimeout(timeoutId);
          console.error('Network fetch error:', fetchError);
          
          if (fetchError.name === 'AbortError') {
            throw new Error('Время ожидания истекло. Попробуйте загрузить файл меньшего размера или проверьте соединение.');
          }
          
          throw new Error('Ошибка соединения с сервером. Проверьте подключение к интернету и попробуйте снова.');
        }

        // Check if response is not JSON (could be HTML error page)
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Server returned non-JSON response:', await response.text());
          throw new Error(`Server error: Received ${contentType} instead of JSON`);
        }

        const result = await response.json();

        if (!response.ok) {
          console.error('Upload failed:', result);
          throw new Error(result.message || `Upload failed with status ${response.status}`);
        }

        if (result.success) {
          // Store the agent ID if it was generated by the server
          if (result.agentId && !agentId) {
            localStorage.setItem('agentId', result.agentId);
            // Also set cookie for backward compatibility
            Cookies.set('agentId', result.agentId, { expires: 7 });
          }

          // Set the uploaded file state and clear any errors
          setUploadedFile(file);
          setError(null);

          // Pass both extracted data and file path
          onExtract(result.data, result.filePath);
        } else {
          throw new Error(result.message || 'Failed to extract data');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to upload and process document';
        setError(errorMessage);
        console.error('File upload error:', {
          error: err,
          message: errorMessage,
          fileName: fileToUpload.name,
          fileSize: fileToUpload.size,
          timestamp: new Date().toISOString()
        });
        // Reset the uploaded file on error
        setUploadedFile(null);
      } finally {
        setIsUploading(false);
      }
    },
    [name, onExtract, maxSizeMB]
  );

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.bmp', '.heic', '.heif'],
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    maxSize: maxSizeMB * 1024 * 1024
  });

  // Show rejection errors
  React.useEffect(() => {
    if (fileRejections.length > 0) {
      const rejection = fileRejections[0];
      if (rejection.errors[0].code === 'file-too-large') {
        setError(`Файл слишком большой. Максимальный размер: ${maxSizeMB}MB`);
      } else {
        setError(`Ошибка: ${rejection.errors[0].message}`);
      }
    }
  }, [fileRejections, maxSizeMB]);

  return (
    <div className="mb-4">
      <label className="block text-gray-700 font-medium mb-2">{label}</label>
      <div
        {...getRootProps()}
        className={`border-2 border-dashed p-6 rounded-lg text-center ${
          isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
        } ${error ? 'border-red-500' : ''} cursor-pointer`}
      >
        <input {...getInputProps()} />
        {isUploading ? (
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-gray-600">Обработка документа...</p>
          </div>
        ) : uploadedFile ? (
          <div>
            <p className="text-green-600 font-medium">Файл загружен: {uploadedFile.name}</p>
            <p className="text-sm text-gray-500 mt-1">Нажмите или перетащите другой файл для замены</p>
          </div>
        ) : hasExistingFile ? (
          <div>
            <p className="text-blue-600 font-medium">Документ уже загружен ранее</p>
            <p className="text-sm text-gray-500 mt-1">Нажмите или перетащите новый файл для замены</p>
          </div>
        ) : (
          <div>
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <p className="mt-2 text-gray-600">
              {isDragActive
                ? 'Перетащите файл сюда...'
                : 'Нажмите или перетащите файл для загрузки'}
            </p>
            <p className="text-xs text-gray-500 mt-1">JPG, PNG, PDF до {maxSizeMB}MB</p>
          </div>
        )}
      </div>
      {error && (
        <div className="mt-2 p-2 bg-red-100 border border-red-400 rounded text-red-700 text-sm">
          <p className="font-medium">Ошибка при обработке документа:</p>
          <p>{error}</p>
          <button
            onClick={() => {
              setError(null);
              // Clear file state to allow retry
              setUploadedFile(null);
            }}
            className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
          >
            Попробовать снова
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;