# Тест валидации загруженных документов в Step2_DocumentUpload

## Описание проблемы
После перезагрузки страницы или возврата с другого шага формы, компонент Step2_DocumentUpload не позволяет перейти к следующему шагу, даже если документы уже были загружены ранее.

## Исправления
1. ✅ Добавлена загрузка `uploaded_files` из базы данных при инициализации в `[slug].tsx`
2. ✅ Обновлена валидация в `Step2_DocumentUpload.tsx` для проверки загруженных файлов
3. ✅ Добавлено отображение статуса уже загруженных документов
4. ✅ Обновлен компонент `FileUpload.tsx` для поддержки существующих файлов

## Тестовый сценарий

### Шаг 1: Первоначальная загрузка документов
1. Открыть форму в браузере
2. Перейти к шагу 2 (Загрузка документов)
3. Загрузить документ (паспорт или удостоверение)
4. Убедиться, что кнопка "Далее" становится активной
5. Перейти к следующему шагу

### Шаг 2: Проверка после перезагрузки страницы
1. Перезагрузить страницу (F5 или Ctrl+R)
2. Убедиться, что форма загружается на том же шаге
3. Проверить, что отображается статус загруженных документов
4. Убедиться, что кнопка "Далее" активна без повторной загрузки документов

### Шаг 3: Проверка после возврата с другого шага
1. Перейти к шагу 3 или далее
2. Вернуться к шагу 2 с помощью кнопки "Назад"
3. Проверить, что отображается статус загруженных документов
4. Убедиться, что кнопка "Далее" активна

### Ожидаемые результаты
- ✅ Зеленый блок с сообщением "Документы загружены" должен отображаться
- ✅ Список загруженных документов должен показывать правильные типы
- ✅ Кнопка "Далее" должна быть активной
- ✅ Компоненты FileUpload должны показывать "Документ уже загружен ранее"

## Логи для отладки
Проверить в консоли браузера следующие сообщения:
- `Loading uploaded files from database:` - загрузка файлов из БД
- `Step2: Document upload status updated:` - обновление статуса документов
- `FileUpload: Found existing file for` - обнаружение существующих файлов

## Проверка в базе данных
В таблице `visa_applications` поле `uploaded_files` должно содержать JSON с загруженными файлами:
```json
{
  "passport": "agent_id/filename.jpg",
  "idCard": "agent_id/filename.jpg"
}
```

## Отладка проблемы "не могу нажать далее"

### Проверьте в консоли браузера (F12 → Console):

1. **Логи загрузки файлов из БД:**
   ```
   Loading uploaded files from database: {...}
   ```

2. **Логи статуса документов:**
   ```
   Step2: Document upload status updated: {hasUploads: true, ...}
   ```

3. **Логи валидации Formik:**
   ```
   Step2: Formik validation state changed: true
   ```

4. **Логи валидации шага:**
   ```
   Step2: Step validity changed: {isStepValid: true, isFormValid: true, isDocumentUploaded: true}
   ```

5. **Логи главного компонента:**
   ```
   [Main] Step 2 validation: {isValid: true, refExists: true, uploadedFilesCount: 1}
   ```

### Если кнопка "Далее" неактивна, проверьте:

1. **Все обязательные поля заполнены:**
   - Фамилия (только латиница)
   - Имя (только латиница)
   - Дата рождения
   - Гражданство
   - Хотя бы один идентификатор (паспорт/ИИН/удостоверение)

2. **Документ загружен:**
   - Должен быть зеленый блок "Документы загружены"
   - Или компонент FileUpload показывает "Документ уже загружен ранее"

3. **Ошибки валидации:**
   - Проверьте красные сообщения об ошибках под полями
   - Убедитесь, что нет ошибок React в консоли

### Возможные проблемы:

1. **Ошибка "uncontrolled to controlled":**
   - Исправлена добавлением значений по умолчанию в Formik

2. **Валидация не обновляется:**
   - Добавлен принудительный вызов validateForm при изменении статуса документов

3. **Состояние не синхронизировано:**
   - Добавлены дополнительные useEffect для синхронизации состояния

## Новые функции

### 1. Условная валидация ИИН для граждан Казахстана

**Что изменилось:**
- В схеме валидации Step2 поле ИИН теперь обязательно для граждан Казахстана
- Используется Yup условная валидация `.when('citizenship', {...})`

**Как тестировать:**
1. Перейти к Step2
2. В поле "Гражданство" выбрать "Казахстан"
3. Попробовать оставить поле ИИН пустым
4. Должна появиться ошибка: "ИИН обязателен для граждан Казахстана"
5. Ввести ИИН (12 цифр) - ошибка должна исчезнуть
6. Изменить гражданство на другую страну - поле ИИН становится необязательным

### 2. Автозаполнение Step3 данными из Step2

**Что изменилось:**
- Данные, извлеченные из документов на Step2, автоматически передаются в Step3
- В Step3 отображается уведомление о автозаполненных полях
- Пользователь может редактировать автозаполненные данные

**Поля для автозаполнения:**
- ФИО на кириллице (генерируется из латинского имени)
- Пол (если извлечен из документа)
- Национальность (если извлечена из документа)
- Место рождения (если извлечено из документа)

**Как тестировать:**
1. Загрузить документ на Step2, который содержит данные о поле, национальности и т.д.
2. Заполнить все обязательные поля Step2 и перейти к Step3
3. В Step3 должен появиться зеленый блок "Данные автоматически заполнены"
4. Соответствующие поля должны быть предзаполнены
5. Проверить, что можно редактировать автозаполненные данные

**Логи для отладки:**
- `Step2: Passing extracted data to parent for cross-step usage:` - передача данных в родительский компонент
- Проверить в консоли, что данные корректно извлекаются и передаются

## Исправление проблемы с валидацией поля ИИН

### Проблема:
После загрузки документа на Step2, кнопка "Далее" не становилась активной сразу при вводе валидного ИИН. Требовалось нажать Enter или переключиться на другое поле.

### Исправления:

1. **Добавлена принудительная валидация при изменении критических полей:**
   - Добавлен useEffect с дебаунсингом (100ms) для полей: ИИН, гражданство, фамилия, имя, дата рождения
   - Валидация вызывается автоматически при изменении этих полей

2. **Улучшена функция validate в Formik:**
   - Добавлена дополнительная функция validate для принудительного обновления состояния валидации

3. **Добавлен дополнительный триггер валидации в главном компоненте:**
   - Создан `step2ValidationTrigger` для принудительного обновления валидации Step2
   - Обновляется с коротким дебаунсингом (50ms) при изменении formData или uploadedFiles

### Тестирование исправления:

**Сценарий 1: Валидация ИИН для граждан Казахстана**
1. Перейти к Step2
2. Загрузить любой документ
3. Выбрать "Казахстан" в поле "Гражданство"
4. Начать вводить ИИН (12 цифр)
5. **Ожидаемый результат:** Кнопка "Далее" должна стать активной сразу после ввода 12-й цифры, без необходимости нажимать Enter или переключаться на другое поле

**Сценарий 2: Валидация других полей**
1. Заполнить фамилию, имя, дату рождения
2. **Ожидаемый результат:** Кнопка "Далее" должна обновляться в реальном времени при заполнении каждого поля

**Логи для отладки:**
```
Step2: Critical values changed, triggering validation: {iin: "123456789012", citizenship: "Казахстан", ...}
Step2: Formik validation state changed: true
[Main] canGoNext calculated: true
[Main] isFormValid calculated: true
```

**Проверка в консоли:**
- При вводе ИИН должны появляться логи `Step2: Critical values changed, triggering validation:`
- Валидация должна обновляться каждые 100ms после изменения поля
- Главный компонент должен получать обновления валидации каждые 50ms
