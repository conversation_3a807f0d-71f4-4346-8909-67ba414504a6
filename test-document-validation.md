# Тест валидации загруженных документов в Step2_DocumentUpload

## Описание проблемы
После перезагрузки страницы или возврата с другого шага формы, компонент Step2_DocumentUpload не позволяет перейти к следующему шагу, даже если документы уже были загружены ранее.

## Исправления
1. ✅ Добавлена загрузка `uploaded_files` из базы данных при инициализации в `[slug].tsx`
2. ✅ Обновлена валидация в `Step2_DocumentUpload.tsx` для проверки загруженных файлов
3. ✅ Добавлено отображение статуса уже загруженных документов
4. ✅ Обновлен компонент `FileUpload.tsx` для поддержки существующих файлов

## Тестовый сценарий

### Шаг 1: Первоначальная загрузка документов
1. Открыть форму в браузере
2. Перейти к шагу 2 (Загрузка документов)
3. Загрузить документ (паспорт или удостоверение)
4. Убедиться, что кнопка "Далее" становится активной
5. Перейти к следующему шагу

### Шаг 2: Проверка после перезагрузки страницы
1. Перезагрузить страницу (F5 или Ctrl+R)
2. Убедиться, что форма загружается на том же шаге
3. Проверить, что отображается статус загруженных документов
4. Убедиться, что кнопка "Далее" активна без повторной загрузки документов

### Шаг 3: Проверка после возврата с другого шага
1. Перейти к шагу 3 или далее
2. Вернуться к шагу 2 с помощью кнопки "Назад"
3. Проверить, что отображается статус загруженных документов
4. Убедиться, что кнопка "Далее" активна

### Ожидаемые результаты
- ✅ Зеленый блок с сообщением "Документы загружены" должен отображаться
- ✅ Список загруженных документов должен показывать правильные типы
- ✅ Кнопка "Далее" должна быть активной
- ✅ Компоненты FileUpload должны показывать "Документ уже загружен ранее"

## Логи для отладки
Проверить в консоли браузера следующие сообщения:
- `Loading uploaded files from database:` - загрузка файлов из БД
- `Step2: Document upload status updated:` - обновление статуса документов
- `FileUpload: Found existing file for` - обнаружение существующих файлов

## Проверка в базе данных
В таблице `visa_applications` поле `uploaded_files` должно содержать JSON с загруженными файлами:
```json
{
  "passport": "agent_id/filename.jpg",
  "idCard": "agent_id/filename.jpg"
}
```
